# 我的Android应用

这是一个使用现代Android开发技术栈创建的示例应用。

## 技术栈

- **语言**: Kotlin
- **UI框架**: Jetpack Compose
- **架构**: Material Design 3
- **构建工具**: Gradle (Kotlin DSL)
- **最低SDK版本**: 24 (Android 7.0)
- **目标SDK版本**: 34 (Android 14)

## 功能特性

- 使用Jetpack Compose构建的现代UI
- Material Design 3主题
- 响应式设计
- 简单的计数器功能演示

## 项目结构

```
app/
├── src/main/
│   ├── java/com/example/myandroidapp/
│   │   ├── MainActivity.kt          # 主Activity
│   │   └── ui/theme/               # UI主题配置
│   │       ├── Color.kt
│   │       ├── Theme.kt
│   │       └── Type.kt
│   ├── res/                        # 资源文件
│   │   ├── drawable/              # 图标资源
│   │   ├── layout/                # 布局文件
│   │   ├── mipmap-*/              # 应用图标
│   │   ├── values/                # 值资源
│   │   └── xml/                   # XML配置
│   └── AndroidManifest.xml        # 应用清单
├── build.gradle.kts               # 模块级构建配置
└── proguard-rules.pro            # ProGuard规则
```

## 如何运行

1. 确保你已经安装了Android Studio
2. 打开项目
3. 等待Gradle同步完成
4. 连接Android设备或启动模拟器
5. 点击运行按钮

## 开发环境要求

- Android Studio Hedgehog | 2023.1.1 或更高版本
- JDK 8 或更高版本
- Android SDK API 34
- Gradle 8.2 或更高版本

## 许可证

此项目仅用于学习和演示目的。
